# 门店选择器性能监控说明

## 概述

门店选择器组件已集成了详细的性能监控功能，可以帮助开发者快速定位性能瓶颈和优化点。

**注意：性能监控仅在开发环境 (NODE_ENV === 'development') 中启用，生产环境自动禁用以避免性能开销。**

## 监控的关键流程

### 1. 初始化流程

- **门店选择器-弹窗打开**: 记录弹窗打开时间和产品ID
- **门店选择器-完整初始化流程**: 从开始初始化到完成的总耗时
- **门店选择器-状态重置**: 组件状态重置耗时
- **门店选择器-获取用户位置**: 调用定位API的耗时
- **门店选择器-逆地理编码**: 坐标转地址的API调用耗时
- **门店选择器-地址信息设置**: 地址信息处理耗时

### 2. 地址选择流程

- **地址选择器-获取省份数据**: 获取省份列表API耗时
- **地址选择器-获取城市数据**: 获取城市列表API耗时
- **地址选择器-获取区县数据**: 获取区县列表API耗时
- **地址选择器-数据格式化**: 地址数据格式化处理耗时
- **地址选择器-地址选择交互**: 用户选择地址的交互响应耗时

### 3. 门店列表获取流程

- **门店列表-完整获取流程**: 门店列表获取的完整流程耗时
- **门店列表-构建请求参数**: 构建API请求参数耗时
- **门店列表-API调用**: 门店列表API调用耗时
- **门店列表-数据处理**: 门店数据处理和过滤耗时

### 4. 地图渲染流程

- **门店选择器-生成地图标记点**: 生成地图标记点数据耗时
- **门店选择器-地图中心点更新**: 地图中心点更新耗时
- **门店选择器-设置默认地图中心**: 设置默认地图中心点耗时

### 5. 用户交互流程

- **门店列表-门店选择交互**: 用户选择门店的交互响应耗时
- **门店列表-滚动到门店**: 滚动到指定门店项的耗时
- **门店选择器-地图标记点击**: 地图标记点击响应耗时
- **门店选择器-查找对应门店**: 根据地图点击查找门店耗时
- **门店选择器-确认门店选择**: 确认选择门店的处理耗时

## 如何查看性能数据

### 1. 实时监控

打开浏览器开发者工具的控制台，在使用门店选择器时会看到带层级缩进的实时性能日志：

```
🚀 [性能监控] 门店选择器-完整初始化流程 - 开始 (父操作: 无)
🚀 [性能监控]   门店选择器-状态重置 - 开始 (父操作: 门店选择器-完整初始化流程)
✅ [性能监控]   门店选择器-状态重置 - 完成 (父操作: 门店选择器-完整初始化流程) { 耗时: "0.70ms", 层级: 1 }
🚀 [性能监控]   门店选择器-获取用户位置 - 开始 (父操作: 门店选择器-完整初始化流程)
✅ [性能监控]   门店选择器-获取用户位置 - 完成 (父操作: 门店选择器-完整初始化流程) { 耗时: "2.32s", 层级: 1 }
📝 [性能监控]   门店选择器-位置信息处理 (父操作: 门店选择器-完整初始化流程) { 经度: 116.397, 纬度: 39.916 }
🚀 [性能监控]   门店选择器-逆地理编码 - 开始 (父操作: 门店选择器-完整初始化流程)
✅ [性能监控]   门店选择器-逆地理编码 - 完成 (父操作: 门店选择器-完整初始化流程) { 耗时: "123.60ms", 层级: 1 }
✅ [性能监控] 门店选择器-完整初始化流程 - 完成 (父操作: 无) { 耗时: "2.45s", 层级: 0 }
```

通过缩进和父操作信息，可以清晰看出操作的嵌套关系和从属关系。

### 2. 性能报告

性能报告会在以下情况自动生成：

- **关闭弹窗时** - 用户点击关闭按钮
- **确认门店选择时** - 用户成功选择门店并确认
- **无门店关闭时** - 选择了地址但该地区无门店

报告格式示例：

```
📊 [性能报告] 门店选择器性能分析

🌳 [操作树] 按执行顺序和层级关系:
├─ 门店选择器-完整初始化流程 (2.45s)
  ├─ 门店选择器-状态重置 (0.70ms) ← 门店选择器-完整初始化流程
  ├─ 门店选择器-获取用户位置 (2.32s) ← 门店选择器-完整初始化流程
  ├─ 门店选择器-逆地理编码 (123.60ms) ← 门店选择器-完整初始化流程
├─ 门店列表-完整获取流程 (564.10ms)
  ├─ 门店列表-状态设置 (0.60ms) ← 门店列表-完整获取流程
  ├─ 门店列表-API调用 (562.60ms) ← 门店列表-完整获取流程

📋 [详细数据表]:
┌─────────┬──────────────────────────────┬────────┬────┬──────────┬──────────┬──────────┐
│ (index) │            操作              │  耗时  │层级│  父操作  │ 开始时间 │ 结束时间 │
├─────────┼──────────────────────────────┼────────┼────┼──────────┼──────────┼──────────┤
│    0    │ 门店选择器-完整初始化流程    │ 2.45s  │ 0  │    无    │ 14:21:16 │ 14:21:19 │
│    1    │ 门店选择器-获取用户位置      │ 2.32s  │ 1  │ 完整初始化│ 14:21:16 │ 14:21:18 │
│    2    │    门店列表-API调用          │ 562ms  │ 1  │ 完整获取 │ 14:21:19 │ 14:21:19 │
└─────────┴──────────────────────────────┴────────┴────┴──────────┴──────────┴──────────┘
⏱️ 总耗时: 6.05s
🐌 最耗时操作: 门店选择器-完整初始化流程 (2.45s)
```

### 3. 性能警告

当某个操作耗时超过2秒时，会显示带层级缩进的性能警告：

```
⚠️ [性能警告]   门店列表-API调用 耗时过长: 3.5s
```

## 性能优化建议

### 根据监控数据进行优化

1. **API调用优化**

   - 如果"门店列表-API调用"耗时过长，考虑优化后端接口或添加缓存
   - 如果"地址选择器-获取省份/城市/区县数据"耗时过长，考虑预加载或缓存地址数据

2. **定位服务优化**

   - 如果"门店选择器-获取用户位置"耗时过长，考虑添加超时机制
   - 如果"门店选择器-逆地理编码"耗时过长，考虑使用更快的地理编码服务

3. **UI交互优化**

   - 如果"门店列表-滚动到门店"耗时过长，考虑使用虚拟滚动
   - 如果"门店选择器-生成地图标记点"耗时过长，考虑优化标记点生成算法

4. **数据处理优化**
   - 如果"门店列表-数据处理"耗时过长，考虑优化数据过滤和格式化逻辑
   - 如果"地址选择器-数据格式化"耗时过长，考虑优化数据转换逻辑

## 控制台命令

在浏览器控制台中，你可以使用以下命令：

```javascript
// 查看当前性能数据
window.performanceMonitor?.getResults()

// 生成性能报告
window.performanceMonitor?.generateReport()

// 清空性能数据
window.performanceMonitor?.clear()

// 启用/禁用性能监控
window.performanceMonitor?.setEnabled(false) // 禁用
window.performanceMonitor?.setEnabled(true) // 启用
```

## 注意事项

1. 性能监控默认启用，如需禁用可在控制台执行相应命令
2. 每次关闭弹窗时会自动清空性能数据，避免内存泄漏
3. 性能数据包含详细的元数据，便于问题定位
4. 超过2秒的操作会自动标记为性能警告
5. 所有时间戳都使用高精度的 performance.now() API

## 扩展监控

如需添加更多监控点，可以在代码中使用：

```typescript
import { performanceMonitor } from './PerformanceMonitor'

// 开始计时
performanceMonitor.start('操作名称', { 元数据: '值' })

// 结束计时
performanceMonitor.end('操作名称', { 额外元数据: '值' })

// 记录即时操作
performanceMonitor.record('操作名称', { 元数据: '值' })
```
